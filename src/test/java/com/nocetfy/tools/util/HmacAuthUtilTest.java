package com.nocetfy.tools.util;

import org.junit.jupiter.api.Test;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * HMAC认证工具类测试
 */
class HmacAuthUtilTest {

    @Test
    void testGetAuthHeaders() {
        // 测试数据
        String accountName = "testuser";
        String secret = "testsecret";

        // 调用方法
        Map<String, String> headers = HmacAuthUtil.getAuthHeaders(accountName, secret);

        // 验证结果
        assertNotNull(headers);
        assertTrue(headers.containsKey("x-date"));
        assertTrue(headers.containsKey("api-user"));
        assertTrue(headers.containsKey("observeauth"));

        String authValue = headers.get("observeauth");
        assertNotNull(authValue);
        assertTrue(authValue.startsWith("hmac username=\"testuser\""));
        assertTrue(authValue.contains("algorithm=\"hmac-sha1\""));
        assertTrue(authValue.contains("headers=\"x-date\""));
        assertTrue(authValue.contains("signature="));

        String apiUser = headers.get("api-user");
        assertEquals("testuser", apiUser);

        String xDate = headers.get("x-date");
        assertNotNull(xDate);
        assertTrue(xDate.endsWith("GMT"));

        System.out.println("Generated headers:");
        headers.forEach((key, value) -> System.out.println(key + ": " + value));
    }

    @Test
    void testGetAuthHeadersWithNullParameters() {
        // 测试空参数
        assertThrows(RuntimeException.class, () -> {
            HmacAuthUtil.getAuthHeaders(null, "secret");
        });

        assertThrows(RuntimeException.class, () -> {
            HmacAuthUtil.getAuthHeaders("username", null);
        });
    }

    @Test
    void testSignatureConsistency() {
        // 测试相同输入在短时间内产生相同签名
        String accountName = "testuser";
        String secret = "testsecret";

        Map<String, String> headers1 = HmacAuthUtil.getAuthHeaders(accountName, secret);
        Map<String, String> headers2 = HmacAuthUtil.getAuthHeaders(accountName, secret);

        // 由于时间可能会变化，我们主要验证格式一致性
        assertEquals(headers1.get("api-user"), headers2.get("api-user"));
        assertTrue(headers1.get("observeauth").contains("algorithm=\"hmac-sha1\""));
        assertTrue(headers2.get("observeauth").contains("algorithm=\"hmac-sha1\""));
    }

    @Test
    void testGetAuthHeadersWithCertAuth() {
        // 测试包含证书认证的头生成
        String accountName = "testuser";
        String secret = "testsecret";
        String certCaller = "testcaller";
        String certSecret = "certsecret";

        // 调用方法
        Map<String, String> headers = HmacAuthUtil.getAuthHeaders(accountName, secret, certCaller, certSecret);

        // 验证HMAC认证头
        assertNotNull(headers);
        assertTrue(headers.containsKey("x-date"));
        assertTrue(headers.containsKey("api-user"));
        assertTrue(headers.containsKey("observeauth"));

        // 验证证书认证头
        assertTrue(headers.containsKey("Authorization"));
        String authValue = headers.get("Authorization");
        assertNotNull(authValue);
        assertTrue(authValue.startsWith("Cert caller=testcaller,token="));

        System.out.println("Generated headers with cert auth:");
        headers.forEach((key, value) -> System.out.println(key + ": " + value));
    }

    @Test
    void testCertAuthOnly() {
        // 测试仅证书认证
        Map<String, String> headers = HmacAuthUtil.getAuthHeaders(null, null, "testcaller", "certsecret");

        // 应该只包含证书认证头
        assertTrue(headers.containsKey("Authorization"));
        assertFalse(headers.containsKey("x-date"));
        assertFalse(headers.containsKey("api-user"));
        assertFalse(headers.containsKey("observeauth"));

        String authValue = headers.get("Authorization");
        assertTrue(authValue.startsWith("Cert caller=testcaller,token="));
    }
}
