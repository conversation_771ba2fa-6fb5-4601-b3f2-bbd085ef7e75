package com.nocetfy.tools.config;

import com.nocetfy.tools.dao.rpc.LogInsight;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.beans.factory.annotation.Autowired;

import static org.junit.jupiter.api.Assertions.*;

/**
 * HTTP客户端配置测试
 */
@SpringBootTest
@TestPropertySource(properties = {
        "log-insight.auth.username=test_user",
        "log-insight.auth.secret=test_secret",
        "log-insight.auth.cert-caller=test_caller",
        "log-insight.auth.cert-secret=test_cert_secret",
        "log-insight.auth.base-url=http://test.example.com"
})
class HttpClientConfigTest {

    @Autowired
    private LogInsight logInsightClient;

    @Autowired
    private LogInsightAuthConfig authConfig;

    @Test
    void testLogInsightClientCreation() {
        assertNotNull(logInsightClient, "LogInsight客户端应该被成功创建");
    }

    @Test
    void testAuthConfigLoading() {
        assertNotNull(authConfig, "认证配置应该被成功加载");
        assertEquals("test_user", authConfig.username());
        assertEquals("test_secret", authConfig.secret());
        assertEquals("test_caller", authConfig.certCaller());
        assertEquals("test_cert_secret", authConfig.certSecret());
        assertEquals("http://test.example.com", authConfig.baseUrl());
    }
}
