package com.nocetfy.tools.config;

import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * LogInsight认证配置
 */
@ConfigurationProperties(prefix = "log-insight.auth")
public record LogInsightAuthConfig(
        String username,

        String secret,

        String certCaller,

        String certSecret,

        String baseUrl
) {

    /**
     * 默认构造器，提供默认的baseUrl
     */
    public LogInsightAuthConfig {
        if (baseUrl == null) {
            baseUrl = "http://your-log-insight-service-url";
        }
    }
}
