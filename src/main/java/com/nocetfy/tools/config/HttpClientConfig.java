package com.nocetfy.tools.config;

import com.nocetfy.tools.dao.rpc.LogInsight;
import com.nocetfy.tools.util.HmacAuthUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.reactive.function.client.ClientRequest;
import org.springframework.web.reactive.function.client.ExchangeFilterFunction;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.support.WebClientAdapter;
import org.springframework.web.service.invoker.HttpServiceProxyFactory;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * HTTP客户端配置
 */
@Configuration
@RequiredArgsConstructor
@EnableConfigurationProperties(LogInsightAuthConfig.class)
public class HttpClientConfig {

    private final LogInsightAuthConfig authConfig;

    /**
     * 创建LogInsight HTTP RPC客户端
     *
     * @return LogInsight客户端实例
     */
    @Bean
    public LogInsight logInsightClient() {
        ExchangeFilterFunction hmacFilter = (request, next) -> next.exchange(ClientRequest.from(request)
                .headers(headers -> {
                    Map<String, String> authHeaders = HmacAuthUtil.getAuthHeaders(
                            authConfig.username(), authConfig.secret(),
                            authConfig.certCaller(), authConfig.certSecret());
                    authHeaders.forEach(headers::add);
                })
                .build());
        WebClient webClient = WebClient.builder()
                .baseUrl(authConfig.baseUrl())
                .defaultHeader("Content-Type", "application/json")
                .defaultHeader("Accept", "application/json")
                .filter(hmacFilter)
                .build();

        HttpServiceProxyFactory factory = HttpServiceProxyFactory
                .builderFor(WebClientAdapter.create(webClient))
                .build();

        return factory.createClient(LogInsight.class);
    }
}
