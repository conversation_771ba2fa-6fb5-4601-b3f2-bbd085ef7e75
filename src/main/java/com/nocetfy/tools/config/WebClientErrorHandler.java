package com.nocetfy.tools.config;

import com.nocetfy.tools.exception.HttpResponseException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.client.ClientResponse;
import org.springframework.web.reactive.function.client.ExchangeFilterFunction;
import reactor.core.publisher.Mono;

/**
 * WebClient错误处理器
 */
@Slf4j
public class WebClientErrorHandler {

    /**
     * 创建响应错误处理过滤器
     */
    public static ExchangeFilterFunction errorHandlingFilter() {
        return ExchangeFilterFunction.ofResponseProcessor(response -> {
            if (response.statusCode().isError()) {
                return handleErrorResponse(response);
            }
            return Mono.just(response);
        });
    }

    /**
     * 处理错误响应
     */
    private static Mono<ClientResponse> handleErrorResponse(ClientResponse response) {
        HttpStatus status = (HttpStatus) response.statusCode();
        MediaType contentType = response.headers().contentType().orElse(MediaType.TEXT_HTML);
        
        log.warn("HTTP请求返回错误状态码: {}, 内容类型: {}", status, contentType);
        
        // 读取响应体
        return response.bodyToMono(String.class)
                .defaultIfEmpty("")
                .flatMap(body -> {
                    log.error("错误响应内容: {}", body.length() > 500 ? body.substring(0, 500) + "..." : body);
                    
                    // 如果是HTML响应，通常表示服务器返回了错误页面
                    if (MediaType.TEXT_HTML.isCompatibleWith(contentType)) {
                        String errorMessage = extractErrorFromHtml(body, status);
                        return Mono.error(new HttpResponseException(status, body, contentType.toString(), errorMessage));
                    }
                    
                    // 其他类型的错误响应
                    return Mono.error(new HttpResponseException(status, body, contentType.toString()));
                });
    }

    /**
     * 从HTML错误页面中提取错误信息
     */
    private static String extractErrorFromHtml(String htmlBody, HttpStatus status) {
        if (htmlBody == null || htmlBody.isEmpty()) {
            return "服务器返回空的HTML错误页面，状态码: " + status;
        }
        
        // 尝试提取HTML title
        String title = extractHtmlTitle(htmlBody);
        if (title != null && !title.isEmpty()) {
            return String.format("服务器返回HTML错误页面: %s (状态码: %s)", title, status);
        }
        
        // 检查常见的错误模式
        if (htmlBody.contains("404") || htmlBody.contains("Not Found")) {
            return "API端点未找到 (404) - 请检查URL路径是否正确";
        }
        
        if (htmlBody.contains("500") || htmlBody.contains("Internal Server Error")) {
            return "服务器内部错误 (500) - 请检查服务器状态";
        }
        
        if (htmlBody.contains("403") || htmlBody.contains("Forbidden")) {
            return "访问被拒绝 (403) - 请检查认证信息";
        }
        
        if (htmlBody.contains("401") || htmlBody.contains("Unauthorized")) {
            return "未授权访问 (401) - 请检查认证凭据";
        }
        
        return String.format("服务器返回HTML错误页面而不是JSON响应，状态码: %s", status);
    }

    /**
     * 提取HTML标题
     */
    private static String extractHtmlTitle(String html) {
        try {
            int titleStart = html.indexOf("<title>");
            int titleEnd = html.indexOf("</title>");
            
            if (titleStart != -1 && titleEnd != -1 && titleEnd > titleStart) {
                return html.substring(titleStart + 7, titleEnd).trim();
            }
        } catch (Exception e) {
            log.debug("提取HTML标题失败", e);
        }
        return null;
    }

    /**
     * 创建请求日志过滤器
     */
    public static ExchangeFilterFunction requestLoggingFilter() {
        return ExchangeFilterFunction.ofRequestProcessor(request -> {
            log.debug("发送HTTP请求: {} {}", request.method(), request.url());
            if (log.isTraceEnabled()) {
                log.trace("请求头: {}", request.headers());
            }
            return Mono.just(request);
        });
    }

    /**
     * 创建响应日志过滤器
     */
    public static ExchangeFilterFunction responseLoggingFilter() {
        return ExchangeFilterFunction.ofResponseProcessor(response -> {
            log.debug("收到HTTP响应: {}", response.statusCode());
            if (log.isTraceEnabled()) {
                log.trace("响应头: {}", response.headers().asHttpHeaders());
            }
            return Mono.just(response);
        });
    }
}
