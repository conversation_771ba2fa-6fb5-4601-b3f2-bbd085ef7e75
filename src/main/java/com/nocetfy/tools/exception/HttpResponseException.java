package com.nocetfy.tools.exception;

import org.springframework.http.HttpStatus;

/**
 * HTTP响应异常
 */
public class HttpResponseException extends RuntimeException {
    
    private final HttpStatus status;
    private final String responseBody;
    private final String contentType;
    
    public HttpResponseException(HttpStatus status, String responseBody, String contentType) {
        super(String.format("HTTP请求失败，状态码: %s, 内容类型: %s", status, contentType));
        this.status = status;
        this.responseBody = responseBody;
        this.contentType = contentType;
    }
    
    public HttpResponseException(HttpStatus status, String responseBody, String contentType, String message) {
        super(message);
        this.status = status;
        this.responseBody = responseBody;
        this.contentType = contentType;
    }
    
    public HttpStatus getStatus() {
        return status;
    }
    
    public String getResponseBody() {
        return responseBody;
    }
    
    public String getContentType() {
        return contentType;
    }
    
    /**
     * 检查是否是HTML错误响应
     */
    public boolean isHtmlResponse() {
        return contentType != null && contentType.contains("text/html");
    }
    
    /**
     * 获取简化的响应体（用于日志）
     */
    public String getSimplifiedResponseBody() {
        if (responseBody == null) {
            return null;
        }
        
        if (responseBody.length() > 500) {
            return responseBody.substring(0, 500) + "...";
        }
        
        return responseBody;
    }
}
