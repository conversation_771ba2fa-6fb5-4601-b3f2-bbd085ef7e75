package com.nocetfy.tools.service;

import com.nocetfy.tools.config.LogInsightAuthConfig;
import com.nocetfy.tools.dao.rpc.LogInsight;
import com.nocetfy.tools.exception.HttpResponseException;
import com.nocetfy.tools.model.dto.LogInsightRespV2;
import com.nocetfy.tools.model.dto.TraceLogParamV2;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

/**
 * 日志洞察服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LogInsightService {

    private final LogInsight logInsightClient;

    /**
     * 查询日志追踪信息
     *
     * @param param 查询参数
     * @return 查询结果
     */
    public LogInsightRespV2 queryTraceLogs(TraceLogParamV2 param) {
        try {
            log.info("开始查询日志追踪信息，参数: {}", param);

            // 首先尝试POST请求
            LogInsightRespV2 result = null;
            try {
                result = logInsightClient.queryTraceLogs(param);
                log.info("POST请求成功");
            } catch (Exception postException) {
                log.warn("POST请求失败，尝试GET请求: {}", postException.getMessage());

                // 如果POST失败，尝试GET请求
                try {
                    result = logInsightClient.queryTraceLogsWithParams(
                            param.startTime(),
                            param.endTime(),
                            param.odinService(),
                            param.traceId(),
                            param.uri(),
                            param.pageSize(),
                            param.dltag(),
                            param.isAsc(),
                            param.messagekey(),
                            param.idc()
                    );
                    log.info("GET请求成功");
                } catch (Exception getException) {
                    log.error("GET请求也失败: {}", getException.getMessage());
                    throw postException; // 抛出原始的POST异常
                }
            }

            log.info("查询日志追踪信息完成，返回{}条记录",
                    result != null && result.data() != null && result.data().list() != null ? result.data().list().size() : 0);
            return result;
        } catch (HttpResponseException e) {
            log.error("HTTP响应错误: {}", e.getMessage());
            if (e.isHtmlResponse()) {
                log.error("服务器返回HTML错误页面，可能的原因：");
                log.error("1. API端点URL不正确");
                log.error("2. 服务器内部错误");
                log.error("3. 认证失败");
                log.error("4. 网络代理或负载均衡器返回错误页面");
                log.error("响应内容: {}", e.getSimplifiedResponseBody());
            }
            throw new RuntimeException("远程服务调用失败: " + e.getMessage(), e);
        } catch (org.springframework.web.client.RestClientException e) {
            log.error("HTTP请求失败: {}", e.getMessage());
            throw new RuntimeException("远程服务调用失败: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("查询日志追踪信息失败", e);
            throw new RuntimeException("查询日志追踪信息失败: " + e.getMessage(), e);
        }
    }

    /**
     * 根据TraceId查询日志
     *
     * @param traceId 链路追踪ID
     * @return 查询结果
     */
    public LogInsightRespV2 queryLogsByTraceId(String traceId) {
        TraceLogParamV2 param = buildWithDefaultTimeRange()
                .traceId(traceId)
                .build();

        return queryTraceLogs(param);
    }

    /**
     * 根据服务名和时间范围查询日志
     *
     * @param odinService 服务名
     * @param startTime   开始时间
     * @param endTime     结束时间
     * @return 查询结果
     */
    public LogInsightRespV2 queryLogsByService(String odinService, Long startTime, Long endTime) {
        TraceLogParamV2 param = TraceLogParamV2.builder()
                .odinService(odinService)
                .startTime(startTime)
                .endTime(endTime)
                .pageSize(100)
                .isAsc(false)
                .build();

        return queryTraceLogs(param);
    }

    /**
     * 根据服务名查询日志（使用默认3分钟时间范围）
     *
     * @param odinService 服务名
     * @return 查询结果
     */
    public LogInsightRespV2 queryLogsByService(String odinService) {
        // 设置时间范围：当前时间往前3分钟
        long endTime = System.currentTimeMillis();
        long startTime = endTime - (3 * 60 * 1000); // 3分钟前

        return queryLogsByService(odinService, startTime, endTime);
    }

    /**
     * 构建默认时间范围参数
     *
     * @return 包含startTime和endTime的参数构建器
     */
    private TraceLogParamV2.Builder buildWithDefaultTimeRange() {
        long endTime = System.currentTimeMillis();
        long startTime = endTime - (10 * 60 * 1000); // 3分钟前

        return TraceLogParamV2.builder()
                .startTime(startTime)
                .endTime(endTime)
                .odinService("pre-sale.gs.biz.didi.com")
                .pageSize(100)
                .isAsc(false);
    }


}
