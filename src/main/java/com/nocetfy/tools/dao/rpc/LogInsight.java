package com.nocetfy.tools.dao.rpc;

import com.nocetfy.tools.model.dto.LogInsightRespV2;
import com.nocetfy.tools.model.dto.TraceLogParamV2;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.GetExchange;
import org.springframework.web.service.annotation.HttpExchange;

/**
 * 日志洞察HTTP RPC接口
 */
@HttpExchange
public interface LogInsight {

    /**
     * 查询日志追踪信息 - 使用对象参数（通过参数展开）
     *
     * @param param 查询参数对象
     * @return 查询结果
     */
    @GetExchange("/loginsight/v1/log/query")
    LogInsightRespV2 queryTraceLogs(@RequestBody TraceLogParamV2 param);
}
