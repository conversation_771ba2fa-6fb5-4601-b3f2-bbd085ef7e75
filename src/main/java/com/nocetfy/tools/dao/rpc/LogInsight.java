package com.nocetfy.tools.dao.rpc;

import com.nocetfy.tools.model.dto.LogInsightRespV2;
import com.nocetfy.tools.model.dto.TraceLogParamV2;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.service.annotation.GetExchange;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;

/**
 * 日志洞察HTTP RPC接口
 */
@HttpExchange
public interface LogInsight {

    /**
     * 查询日志追踪信息 - 使用POST请求和RequestBody
     *
     * @param param 查询参数对象
     * @return 查询结果
     */
    @PostExchange("/loginsight/v1/log/query")
    LogInsightRespV2 queryTraceLogs(@RequestBody TraceLogParamV2 param);

    /**
     * 查询日志追踪信息 - 使用GET请求和查询参数
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param odinService 服务名
     * @param traceId 链路追踪ID
     * @param uri URI
     * @param pageSize 页面大小
     * @param dltag 标签
     * @param isAsc 是否升序
     * @param messagekey 消息键
     * @param idc IDC
     * @return 查询结果
     */
    @GetExchange("/loginsight/v1/log/query")
    LogInsightRespV2 queryTraceLogsWithParams(
            @RequestParam(required = false) Long startTime,
            @RequestParam(required = false) Long endTime,
            @RequestParam(required = false) String odinService,
            @RequestParam(required = false) String traceId,
            @RequestParam(required = false) String uri,
            @RequestParam(required = false) Integer pageSize,
            @RequestParam(required = false) String dltag,
            @RequestParam(required = false) Boolean isAsc,
            @RequestParam(required = false) String messagekey,
            @RequestParam(required = false) Integer idc
    );
}
