package com.nocetfy.tools.controller;

import com.nocetfy.tools.config.LogInsightAuthConfig;
import com.nocetfy.tools.model.dto.LogInsightRespV2;
import com.nocetfy.tools.model.dto.TraceLogParamV2;
import com.nocetfy.tools.service.LogInsightService;
import com.nocetfy.tools.util.ConfigValidator;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 日志洞察控制器
 */
@RestController
@RequestMapping("/log-insight")
@RequiredArgsConstructor
public class LogInsightController {

    private final LogInsightService logInsightService;
    private final LogInsightAuthConfig authConfig;
    private final ConfigValidator configValidator;
    
    /**
     * 查询日志追踪信息
     *
     * @param param 查询参数
     * @return 查询结果
     */
    @PostMapping("/query")
    public LogInsightRespV2 queryTraceLogs(@RequestBody TraceLogParamV2 param) {
        return logInsightService.queryTraceLogs(param);
    }

    /**
     * 根据TraceId查询日志
     *
     * @param traceId 链路追踪ID
     * @return 查询结果
     */
    @GetMapping("/trace/{traceId}")
    public LogInsightRespV2 queryLogsByTraceId(@PathVariable String traceId) {
        return logInsightService.queryLogsByTraceId(traceId);
    }

    /**
     * 根据服务名和时间范围查询日志
     *
     * @param odinService 服务名
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 查询结果
     */
    @GetMapping("/service/{odinService}")
    public LogInsightRespV2 queryLogsByService(
            @PathVariable String odinService,
            @RequestParam Long startTime,
            @RequestParam Long endTime) {
        return logInsightService.queryLogsByService(odinService, startTime, endTime);
    }

    /**
     * 测试配置是否正确
     *
     * @return 配置信息
     */
    @GetMapping("/test-config")
    public Map<String, Object> testConfig() {
        Map<String, Object> config = new HashMap<>();

        // 验证配置
        ConfigValidator.ValidationResult validation = configValidator.validateLogInsightConfig(authConfig);

        config.put("baseUrl", authConfig.baseUrl());
        config.put("username", authConfig.username() != null ? "已配置" : "未配置");
        config.put("secret", authConfig.secret() != null ? "已配置" : "未配置");
        config.put("certCaller", authConfig.certCaller() != null ? "已配置" : "未配置");
        config.put("certSecret", authConfig.certSecret() != null ? "已配置" : "未配置");

        config.put("isValid", validation.isValid());
        config.put("errors", validation.getErrors());
        config.put("warnings", validation.getWarnings());
        config.put("timestamp", java.time.LocalDateTime.now().toString());

        return config;
    }

    /**
     * 测试API连接
     *
     * @return 测试结果
     */
    @GetMapping("/test-connection")
    public Map<String, Object> testConnection() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 创建一个简单的测试参数
            TraceLogParamV2 testParam = TraceLogParamV2.builder()
                    .startTime(System.currentTimeMillis() - 60000) // 1分钟前
                    .endTime(System.currentTimeMillis())
                    .pageSize(1)
                    .build();

            LogInsightRespV2 response = logInsightService.queryTraceLogs(testParam);

            result.put("status", "SUCCESS");
            result.put("message", "API连接测试成功");
            result.put("hasData", response != null && response.data() != null);
            result.put("recordCount", response != null && response.data() != null && response.data().list() != null ?
                    response.data().list().size() : 0);

        } catch (Exception e) {
            result.put("status", "ERROR");
            result.put("message", "API连接测试失败: " + e.getMessage());
            result.put("errorType", e.getClass().getSimpleName());
        }

        result.put("timestamp", java.time.LocalDateTime.now().toString());
        return result;
    }


}
