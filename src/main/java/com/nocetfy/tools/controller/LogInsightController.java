package com.nocetfy.tools.controller;

import com.nocetfy.tools.model.dto.LogInsightRespV2;
import com.nocetfy.tools.model.dto.TraceLogParamV2;
import com.nocetfy.tools.service.LogInsightService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 日志洞察控制器
 */
@RestController
@RequestMapping("/log-insight")
@RequiredArgsConstructor
public class LogInsightController {
    
    private final LogInsightService logInsightService;
    
    /**
     * 查询日志追踪信息
     *
     * @param param 查询参数
     * @return 查询结果
     */
    @PostMapping("/query")
    public LogInsightRespV2 queryTraceLogs(@RequestBody TraceLogParamV2 param) {
        return logInsightService.queryTraceLogs(param);
    }

    /**
     * 根据TraceId查询日志
     *
     * @param traceId 链路追踪ID
     * @return 查询结果
     */
    @GetMapping("/trace/{traceId}")
    public LogInsightRespV2 queryLogsByTraceId(@PathVariable String traceId) {
        return logInsightService.queryLogsByTraceId(traceId);
    }

    /**
     * 根据服务名和时间范围查询日志
     *
     * @param odinService 服务名
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 查询结果
     */
    @GetMapping("/service/{odinService}")
    public LogInsightRespV2 queryLogsByService(
            @PathVariable String odinService,
            @RequestParam Long startTime,
            @RequestParam Long endTime) {
        return logInsightService.queryLogsByService(odinService, startTime, endTime);
    }

    /**
     * 测试配置是否正确
     *
     * @return 配置信息
     */
    @GetMapping("/test-config")
    public Map<String, String> testConfig() {
        Map<String, String> config = new HashMap<>();
        config.put("status", "OK");
        config.put("message", "LogInsight配置正常");
        config.put("timestamp", java.time.LocalDateTime.now().toString());
        return config;
    }


}
