package com.nocetfy.tools.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import org.apache.logging.log4j.message.Message;

import java.util.List;

/**
 * 日志洞察响应V2
 */
public record LogInsightRespV2(
        @JsonProperty("data") Data data,
        @JsonProperty("metaInfo") MetaInfo metaInfo
) {
    
    /**
     * 响应数据
     */
    public record Data(
            @JsonProperty("list") List<LogInsightItem> list
    ) {}
    
    /**
     * 元信息
     */
    public record MetaInfo(
            @JsonProperty("code") int code,
            @JsonProperty("msg") String msg
    ) {}

    /**
     * 日志洞察项
     */
    public record LogInsightItem(
            @JsonProperty("logTime") String logTime,
            @JsonProperty("logTimeStamp") Long logTimeStamp,
            @JsonProperty("hostName") String hostName,
            @JsonProperty("dltag") String dltag,
            @JsonProperty("odinSu") String odinSu,
            @JsonProperty("appName") String appName,
            @JsonProperty("odinLeaf") String odinLeaf,
            @JsonProperty("traceId") String traceId,
            @JsonProperty("spanId") String spanId,
            @JsonProperty("cspanId") String cspanId,
            @JsonProperty("logName") String logName,
            @JsonProperty("extractLevel") String extractLevel,
            @JsonProperty("message") String message,
            @JsonProperty("msg") Message msg,
            @JsonProperty("errNo") String errNo,
            @JsonProperty("errMsg") String errMsg,
            @JsonProperty("uri") String uri
    ) {}

    /**
     * 消息详情
     */
    public record Message(
            @JsonProperty("log_level") String logLevel,
            @JsonProperty("log_time") String logTime,
            @JsonProperty("line") String line,
            @JsonProperty("dltag") String dltag,
            @JsonProperty("traceid") String traceId,
            @JsonProperty("hintCode") String hintCode,
            @JsonProperty("spanid") String spanId,
            @JsonProperty("logid") String logId,
            @JsonProperty("sampling") String sampling,
            @JsonProperty("uri") String uri,
            @JsonProperty("module") String module,
            @JsonProperty("controller") String controller,
            @JsonProperty("hintContent") String hintContent,
            @JsonProperty("url") String url,
            @JsonProperty("from") String from,
            @JsonProperty("args") String args,
            @JsonProperty("response") String response,
            @JsonProperty("trace_origin") String traceOrigin,
            @JsonProperty("caller") String caller,
            @JsonProperty("callee") String callee,
            @JsonProperty("rpc_name") String rpcName,
            @JsonProperty("caller_func") String callerFunc,
            @JsonProperty("callee_func") String calleeFunc,
            @JsonProperty("request") String request
    ) {}
}
