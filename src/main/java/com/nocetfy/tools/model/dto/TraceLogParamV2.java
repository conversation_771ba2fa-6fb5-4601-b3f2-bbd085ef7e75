package com.nocetfy.tools.model.dto;

/**
 * 日志查询参数V2
 */
public record TraceLogParamV2(
        Long startTime,
        Long endTime,
        String odinService,
        String traceId,
        String uri,
        Integer pageSize,
        String dltag,
        Boolean isAsc,
        String messagekey,
        Integer idc
) {

    /**
     * 创建一个新的TraceLogParamV2实例的构建器
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * 构建器类，用于方便地创建TraceLogParamV2实例
     */
    public static class Builder {
        private Long startTime;
        private Long endTime;
        private String odinService;
        private String traceId;
        private String uri;
        private Integer pageSize;
        private String dltag;
        private Boolean isAsc;
        private String messagekey;
        private Integer idc;

        public Builder startTime(Long startTime) {
            this.startTime = startTime;
            return this;
        }

        public Builder endTime(Long endTime) {
            this.endTime = endTime;
            return this;
        }

        public Builder odinService(String odinService) {
            this.odinService = odinService;
            return this;
        }

        public Builder traceId(String traceId) {
            this.traceId = traceId;
            return this;
        }

        public Builder uri(String uri) {
            this.uri = uri;
            return this;
        }

        public Builder pageSize(Integer pageSize) {
            this.pageSize = pageSize;
            return this;
        }

        public Builder dltag(String dltag) {
            this.dltag = dltag;
            return this;
        }

        public Builder isAsc(Boolean isAsc) {
            this.isAsc = isAsc;
            return this;
        }

        public Builder messagekey(String messagekey) {
            this.messagekey = messagekey;
            return this;
        }

        public Builder idc(Integer idc) {
            this.idc = idc;
            return this;
        }

        public TraceLogParamV2 build() {
            return new TraceLogParamV2(startTime, endTime, odinService, traceId,
                    uri, pageSize, dltag, isAsc, messagekey, idc);
        }
    }
}
