package com.nocetfy.tools.util;

import com.nocetfy.tools.config.LogInsightAuthConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.List;

/**
 * 配置验证工具
 */
@Slf4j
@Component
public class ConfigValidator {

    /**
     * 验证LogInsight配置
     */
    public ValidationResult validateLogInsightConfig(LogInsightAuthConfig config) {
        List<String> errors = new ArrayList<>();
        List<String> warnings = new ArrayList<>();

        // 验证基础URL
        if (config.baseUrl() == null || config.baseUrl().trim().isEmpty()) {
            errors.add("baseUrl不能为空");
        } else {
            try {
                URI uri = new URI(config.baseUrl());
                if (uri.getScheme() == null) {
                    errors.add("baseUrl必须包含协议 (http:// 或 https://)");
                }
                if (uri.getHost() == null) {
                    errors.add("baseUrl必须包含有效的主机名");
                }
            } catch (URISyntaxException e) {
                errors.add("baseUrl格式无效: " + e.getMessage());
            }
        }

        // 验证HMAC认证配置
        if (config.username() == null || config.username().trim().isEmpty()) {
            warnings.add("HMAC用户名为空，将无法使用HMAC认证");
        }
        if (config.secret() == null || config.secret().trim().isEmpty()) {
            warnings.add("HMAC密钥为空，将无法使用HMAC认证");
        }

        // 验证证书认证配置
        if (config.certCaller() == null || config.certCaller().trim().isEmpty()) {
            warnings.add("证书调用者为空，将无法使用证书认证");
        }
        if (config.certSecret() == null || config.certSecret().trim().isEmpty()) {
            warnings.add("证书密钥为空，将无法使用证书认证");
        }

        // 检查是否至少有一种认证方式可用
        boolean hasHmacAuth = config.username() != null && !config.username().trim().isEmpty() &&
                             config.secret() != null && !config.secret().trim().isEmpty();
        boolean hasCertAuth = config.certCaller() != null && !config.certCaller().trim().isEmpty() &&
                             config.certSecret() != null && !config.certSecret().trim().isEmpty();

        if (!hasHmacAuth && !hasCertAuth) {
            errors.add("至少需要配置一种认证方式（HMAC或证书认证）");
        }

        return new ValidationResult(errors, warnings);
    }

    /**
     * 验证结果
     */
    public static class ValidationResult {
        private final List<String> errors;
        private final List<String> warnings;

        public ValidationResult(List<String> errors, List<String> warnings) {
            this.errors = errors != null ? errors : new ArrayList<>();
            this.warnings = warnings != null ? warnings : new ArrayList<>();
        }

        public boolean isValid() {
            return errors.isEmpty();
        }

        public List<String> getErrors() {
            return errors;
        }

        public List<String> getWarnings() {
            return warnings;
        }

        public boolean hasWarnings() {
            return !warnings.isEmpty();
        }

        public void logResults() {
            if (!isValid()) {
                log.error("配置验证失败:");
                errors.forEach(error -> log.error("  错误: {}", error));
            }

            if (hasWarnings()) {
                log.warn("配置验证警告:");
                warnings.forEach(warning -> log.warn("  警告: {}", warning));
            }

            if (isValid() && !hasWarnings()) {
                log.info("配置验证通过");
            }
        }
    }
}
