# HTTP客户端故障排除指南

## 问题描述
当API接口期望返回JSON格式数据，但实际返回text/html时的兼容性处理方案。

## 改进内容

### 1. 增强的错误处理
- 新增 `HttpResponseException` 异常类，专门处理HTTP响应错误
- 新增 `WebClientErrorHandler` 错误处理器，自动识别和处理HTML错误响应
- 改进日志记录，提供更详细的错误信息

### 2. 接口兼容性改进
- 修改 `LogInsight` 接口，同时支持POST和GET请求方式
- POST请求使用 `@RequestBody`，GET请求使用 `@RequestParam`
- 服务层自动尝试两种请求方式，提高兼容性

### 3. 配置验证
- 新增 `ConfigValidator` 配置验证工具
- 自动检查认证配置的完整性和有效性
- 提供详细的配置错误和警告信息

### 4. 调试工具
- 增强的测试端点 `/log-insight/test-config` 和 `/log-insight/test-connection`
- 详细的请求/响应日志记录
- 配置状态检查

## 使用方法

### 1. 检查配置
```bash
curl http://localhost:8080/log-insight/test-config
```

### 2. 测试连接
```bash
curl http://localhost:8080/log-insight/test-connection
```

### 3. 查看日志
启用DEBUG级别日志来查看详细的HTTP请求/响应信息：
```yaml
logging:
  level:
    com.nocetfy.tools: DEBUG
    org.springframework.web.reactive.function.client: DEBUG
```

## 常见问题排查

### 1. 返回HTML而不是JSON
**可能原因：**
- API端点URL不正确（404错误）
- 服务器内部错误（500错误）
- 认证失败（401/403错误）
- 网络代理或负载均衡器返回错误页面

**排查步骤：**
1. 检查 `base-url` 配置是否正确
2. 验证API端点路径 `/loginsight/v1/log/query` 是否存在
3. 检查认证信息是否正确
4. 查看错误响应的HTML内容获取更多信息

### 2. 认证失败
**排查步骤：**
1. 验证HMAC认证参数：`username`, `secret`
2. 验证证书认证参数：`cert-caller`, `cert-secret`
3. 检查时间同步（HMAC认证对时间敏感）
4. 确认至少配置了一种认证方式

### 3. 网络连接问题
**排查步骤：**
1. 检查网络连通性
2. 验证防火墙设置
3. 检查代理配置
4. 确认DNS解析正常

## 配置示例

```yaml
log-insight:
  auth:
    username: your_username
    secret: your_secret
    cert-caller: your_caller
    cert-secret: your_cert_secret
    base-url: http://your-api-server.com/
```

## 错误代码说明

- **HttpResponseException**: HTTP响应错误，包含状态码和响应内容
- **RestClientException**: 网络连接或HTTP客户端错误
- **RuntimeException**: 其他运行时错误

## 日志级别建议

- **生产环境**: INFO级别
- **测试环境**: DEBUG级别
- **开发调试**: TRACE级别

## 性能优化建议

1. 合理设置连接超时和读取超时
2. 使用连接池复用连接
3. 避免频繁的配置验证
4. 适当的日志级别设置
